import { WifiOff } from '@assets/images';
import { useCharge } from '@bp/charge-mfe';
import { SimpleNotification } from '@bp/ui-components/mobile/core';
import React, { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';

import { useNotificationSound } from '../../hooks/useNotificationSound';
import { useConnectivity } from '../../providers/ConnectivityProvider';

const Notifications = () => {
  const { isCharging: isChargingHook } = useCharge();
  const { isInternetReachable } = useConnectivity();
  const { t } = useTranslation();

  const [isConnected, setIsConnected] = useState(true);
  const [isCharging, setIsCharging] = useState(false);

  // Use the custom notification sound hook
  const { playSound: playNotificationSound } = useNotificationSound();

  useEffect(() => {
    if (isChargingHook !== isCharging) {
      setIsCharging(isChargingHook);

      // Play sound when charging starts
      if (isChargingHook) {
        playNotificationSound();
      }
    }

    if (isInternetReachable !== isConnected) {
      setIsConnected(isInternetReachable);

      // Play sound when connection status changes (lost connection)
      if (!isInternetReachable) {
        playNotificationSound();
      }
    }
  }, [
    isInternetReachable,
    isChargingHook,
    isCharging,
    isConnected,
    playNotificationSound,
  ]);

  if (!isConnected && !isCharging) {
    return (
      <SimpleNotification
        title={t('notifications.lostConnection.title')}
        icon={<WifiOff />}
      />
    );
  }

  if (!isConnected && isCharging) {
    return (
      <SimpleNotification
        icon={<WifiOff />}
        title={t('notifications.lostConnection.title')}
        text={t('notifications.lostConnection.text')}
      />
    );
  }
  return null;
};

export default Notifications;
