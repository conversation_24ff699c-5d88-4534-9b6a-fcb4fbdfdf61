import { useCallback, useEffect, useState } from 'react';
import { Platform } from 'react-native';
import Sound from 'react-native-sound';

import {
  getIOSSoundPaths,
  getSoundFileName,
  NOTIFICATION_SOUND_CONFIG,
  soundLogger,
} from '../config/notificationSound';

// Initialize sound category for playback
Sound.setCategory('Playback');

interface UseNotificationSoundOptions {
  soundFileName?: string;
  volume?: number;
  enabled?: boolean;
}

interface UseNotificationSoundReturn {
  playSound: () => void;
  isLoaded: boolean;
  isEnabled: boolean;
  setEnabled: (enabled: boolean) => void;
}

/**
 * Custom hook for playing notification sounds
 * @param options Configuration options for the sound
 * @returns Object with playSound function and loading state
 */
export const useNotificationSound = (
  options: UseNotificationSoundOptions = {},
): UseNotificationSoundReturn => {
  const {
    soundFileName = getSoundFileName(),
    volume = NOTIFICATION_SOUND_CONFIG.DEFAULTS.VOLUME,
    enabled = NOTIFICATION_SOUND_CONFIG.DEFAULTS.ENABLED,
  } = options;

  const [sound, setSound] = useState<Sound | null>(null);
  const [isLoaded, setIsLoaded] = useState(false);
  const [isEnabled, setIsEnabled] = useState(enabled);

  // Play notification sound with error handling
  const playSound = useCallback(() => {
    if (sound && isLoaded && isEnabled) {
      sound.setVolume(volume);
      sound.play(success => {
        if (!success) {
          soundLogger.warn('Failed to play notification sound');
        }
      });
    } else {
      soundLogger.verbose(
        `Sound playback skipped - loaded: ${isLoaded}, enabled: ${isEnabled}`,
      );
    }
  }, [sound, isLoaded, isEnabled, volume]);

  // Initialize sound on hook mount
  useEffect(() => {
    let notificationSound: Sound | null = null;

    const initializeSound = () => {
      // Platform-specific sound loading with fallback paths
      const tryLoadSound = (
        paths: string[],
        sourceType: any,
        index = 0,
      ): void => {
        if (index >= paths.length) {
          soundLogger.warn(
            'Failed to load sound from all attempted paths - sound will be disabled',
          );
          setIsLoaded(false);
          setIsEnabled(false);
          return;
        }

        const currentPath = paths[index];
        soundLogger.verbose(
          `Attempting to load sound from path: ${currentPath}`,
        );

        notificationSound = new Sound(currentPath, sourceType, error => {
          if (error) {
            soundLogger.verbose(
              `Failed to load sound from path: ${currentPath}`,
              error,
            );
            tryLoadSound(paths, sourceType, index + 1);
            return;
          }

          soundLogger.info(
            `Sound loaded successfully from: ${currentPath} (${Platform.OS})`,
          );
          setIsLoaded(true);
          setSound(notificationSound);
        });
      };

      if (Platform.OS === 'ios') {
        try {
          const iosPaths = getIOSSoundPaths();
          soundLogger.verbose(
            `iOS: Attempting to load sound file: ${soundFileName}`,
          );
          soundLogger.verbose('iOS: Trying paths:', iosPaths);
          tryLoadSound(iosPaths, Sound.MAIN_BUNDLE);
        } catch (iosError) {
          soundLogger.warn('iOS: Error setting up sound paths:', iosError);
          setIsLoaded(false);
        }
      } else {
        // Android: try res/raw path (no extension needed)
        const androidPaths = [
          soundFileName,
          `${soundFileName}.mp3`, // Fallback with extension
        ];
        tryLoadSound(androidPaths, Sound.MAIN_BUNDLE);
      }
    };

    initializeSound();

    return () => {
      if (notificationSound) {
        notificationSound.release();
        setIsLoaded(false);
        setSound(null);
      }
    };
  }, [soundFileName]);

  const setEnabled = useCallback((enableSound: boolean) => {
    setIsEnabled(enableSound);
  }, []);

  return {
    playSound,
    isLoaded,
    isEnabled,
    setEnabled,
  };
};
