import { useCallback, useEffect, useState } from 'react';
import { Platform } from 'react-native';
import { Audio } from 'expo-av';

import {
  getSoundFileName,
  NOTIFICATION_SOUND_CONFIG,
  soundLogger,
} from '../config/notificationSound';

interface UseNotificationSoundOptions {
  soundFileName?: string;
  volume?: number;
  enabled?: boolean;
}

interface UseNotificationSoundReturn {
  playSound: () => void;
  isLoaded: boolean;
  isEnabled: boolean;
  setEnabled: (enabled: boolean) => void;
}

/**
 * Custom hook for playing notification sounds
 * @param options Configuration options for the sound
 * @returns Object with playSound function and loading state
 */
export const useNotificationSound = (
  options: UseNotificationSoundOptions = {},
): UseNotificationSoundReturn => {
  const {
    soundFileName = getSoundFileName(),
    volume = NOTIFICATION_SOUND_CONFIG.DEFAULTS.VOLUME,
    enabled = NOTIFICATION_SOUND_CONFIG.DEFAULTS.ENABLED,
  } = options;

  const [sound, setSound] = useState<Audio.Sound | null>(null);
  const [isLoaded, setIsLoaded] = useState(false);
  const [isEnabled, setIsEnabled] = useState(enabled);

  // Play notification sound with error handling
  const playSound = useCallback(async () => {
    if (sound && isLoaded && isEnabled) {
      try {
        await sound.setVolumeAsync(volume);
        await sound.replayAsync();
      } catch (error) {
        soundLogger.warn('Failed to play notification sound:', error);
      }
    } else {
      soundLogger.verbose(
        `Sound playback skipped - loaded: ${isLoaded}, enabled: ${isEnabled}`,
      );
    }
  }, [sound, isLoaded, isEnabled, volume]);

  // Initialize sound on hook mount
  useEffect(() => {
    let notificationSound: Audio.Sound | null = null;

    const initializeSound = async () => {
      try {
        // Set audio mode for playback
        await Audio.setAudioModeAsync({
          allowsRecordingIOS: false,
          staysActiveInBackground: false,
          playsInSilentModeIOS: true,
          shouldDuckAndroid: true,
          playThroughEarpieceAndroid: false,
        });

        // Create sound object
        // Map dynamic file names to static `require` calls so Metro can bundle them
        const assetMap: Record<string, number> = {
          notification_158187: require('../../assets/sounds/notification_158187.mp3'),
          /* add additional sounds here */
        };
        const asset = assetMap[soundFileName];
        if (!asset) {
          throw new Error(`Unknown notification sound "${soundFileName}"`);
        }
        const { sound: audioSound } = await Audio.Sound.createAsync(
          asset,
          {
            shouldPlay: false,
            volume: volume,
          }
        )

        notificationSound = audioSound;
        setSound(audioSound);
        setIsLoaded(true);

        soundLogger.info(
          `Sound loaded successfully: ${soundFileName} (${Platform.OS})`,
        );
      } catch (error) {
        soundLogger.warn('Failed to load notification sound:', error);
        setIsLoaded(false);
        setIsEnabled(false);
      }
    };

    initializeSound();

    return () => {
      if (notificationSound) {
        notificationSound.unloadAsync();
        setIsLoaded(false);
        setSound(null);
      }
    };
  }, [soundFileName, volume]);

  const setEnabled = useCallback((enableSound: boolean) => {
    setIsEnabled(enableSound);
  }, []);

  return {
    playSound,
    isLoaded,
    isEnabled,
    setEnabled,
  };
};
